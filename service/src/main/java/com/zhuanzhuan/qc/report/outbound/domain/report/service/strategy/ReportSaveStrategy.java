package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.ReportSaveContext;

/**
 * 报告保存策略接口
 * 定义不同报告类型的具体保存逻辑
 */
public interface ReportSaveStrategy<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> {
    
    /**
     * 获取策略支持的报告类型
     */
    String getSupportedReportType();
    
    /**
     * 前置校验
     */
    default void preValidate(ReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 前置处理
     */
    default void preProcess(ReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 基本信息处理
     */
    void processBasicInfo(ReportSaveContext<T, R> context);
    
    /**
     * 模板查询
     */
    void queryTemplate(ReportSaveContext<T, R> context);
    
    /**
     * 报告生成
     */
    void generateReport(ReportSaveContext<T, R> context);
    
    /**
     * 报告处理
     */
    default void processReport(ReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 报告映射
     */
    void mapReport(ReportSaveContext<T, R> context);
    
    /**
     * 后置处理数据写入
     */
    void postProcess(ReportSaveContext<T, R> context);
    
    /**
     * 异常处理
     */
    default void handleException(ReportSaveContext<T, R> context, Exception e) {
        // 默认实现，子类可重写
    }
    
    /**
     * 创建结果对象
     */
    R createResult();
}
