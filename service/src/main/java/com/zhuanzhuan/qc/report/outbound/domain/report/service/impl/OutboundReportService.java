package com.zhuanzhuan.qc.report.outbound.domain.report.service.impl;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveCapturedPhotoPostSaleInspectionCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveOnsiteServiceCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveCapturedPhotoPostSaleInspectionDTO;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveOnsiteServiceDTO;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.IOutboundReportService;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.factory.ReportSaveStrategyFactory;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.template.ReportSaveTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundReportService implements IOutboundReportService {

    private final ReportSaveTemplate reportSaveTemplate;
    private final ReportSaveStrategyFactory strategyFactory;

    @Override
    public SaveRecyclingReportDTO saveRecyclingReport(SaveRecyclingReportCmd cmd) {
        log.info("开始保存回收质检报告, qcCode: {}", cmd.getQcCode());
        var strategy = strategyFactory.getStrategy(cmd.getReportType());
        return reportSaveTemplate.saveReport(cmd, strategy);
    }

    @Override
    public SaveOnsiteServiceDTO saveOnsiteServiceReport(SaveOnsiteServiceCmd cmd) {
        log.info("开始保存上门售后报告, qcCode: {}", cmd.getQcCode());
        var strategy = strategyFactory.getStrategy(cmd.getReportType());
        return reportSaveTemplate.saveReport(cmd, strategy);
    }

    @Override
    public SaveCapturedPhotoPostSaleInspectionDTO saveCapturedPhotoPostSaleInspectionReport(SaveCapturedPhotoPostSaleInspectionCmd cmd) {
        log.info("开始保存售前拍照质检报告, qcCode: {}", cmd.getQcCode());
        var strategy = strategyFactory.getStrategy(cmd.getReportType());
        return reportSaveTemplate.saveReport(cmd, strategy);
    }

}
