package com.zhuanzhuan.qc.report.outbound.domain.report.service.context;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.ReportSaveStrategy;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.model.QcTemplateBO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 报告保存上下文
 * 在整个保存流程中传递数据和状态
 */
@Data
public class ReportSaveContext<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> {
    
    /**
     * 输入命令
     */
    private final T command;
    
    /**
     * 保存策略
     */
    private final ReportSaveStrategy<T, R> strategy;
    
    /**
     * 处理结果
     */
    private R result;
    
    /**
     * 质检模板
     */
    private QcTemplateBO template;
    
    /**
     * 报告数据
     */
    private Object reportData;
    
    /**
     * 处理过程中的临时数据
     */
    private final Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 当前处理步骤
     */
    private String currentStep;
    
    /**
     * 是否跳过某些步骤的标识
     */
    private final Map<String, Boolean> skipFlags = new HashMap<>();
    
    public ReportSaveContext(T command, ReportSaveStrategy<T, R> strategy) {
        this.command = command;
        this.strategy = strategy;
    }
    
    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <V> V getAttribute(String key) {
        return (V) attributes.get(key);
    }
    
    /**
     * 获取属性，带默认值
     */
    @SuppressWarnings("unchecked")
    public <V> V getAttribute(String key, V defaultValue) {
        return (V) attributes.getOrDefault(key, defaultValue);
    }
    
    /**
     * 设置跳过标识
     */
    public void setSkipFlag(String step, boolean skip) {
        skipFlags.put(step, skip);
    }
    
    /**
     * 检查是否跳过某步骤
     */
    public boolean shouldSkip(String step) {
        return skipFlags.getOrDefault(step, false);
    }
    
    /**
     * 获取报告类型
     */
    public String getReportType() {
        return command.getReportType();
    }
}
