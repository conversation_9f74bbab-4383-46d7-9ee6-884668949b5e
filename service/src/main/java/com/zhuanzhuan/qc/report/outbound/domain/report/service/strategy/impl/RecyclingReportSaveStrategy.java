package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.impl;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.ReportSaveContext;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.AbstractReportSaveStrategy;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.IQcTemplateManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 回收质检报告保存策略
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RecyclingReportSaveStrategy extends AbstractReportSaveStrategy<SaveRecyclingReportCmd, SaveRecyclingReportDTO> {

    private final IQcTemplateManager qcTemplateManager;

    @Override
    public String getSupportedReportType() {
        return "SaveRecyclingReport";
    }

    @Override
    public void processBasicInfo(ReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("处理回收质检报告基本信息, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        // 设置基本信息到上下文
        context.setAttribute("qcCode", command.getQcCode());
        context.setAttribute("businessLineId", command.getBusinessLineId());
        context.setAttribute("operatorId", command.getOperatorId());
        context.setAttribute("operatorName", command.getOperatorName());
        
        // 这里可以添加回收报告特有的基本信息处理逻辑
        // 例如：获取商品信息、订单信息等
        
        log.debug("回收质检报告基本信息处理完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void queryTemplate(ReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("查询回收质检模板, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        try {
            // 这里需要根据实际业务逻辑获取模板查询所需的参数
            // 暂时使用默认值，实际使用时需要从command或其他地方获取
            Long businessLineId = command.getBusinessLineId();
            Integer cateId = context.getAttribute("cateId", 1); // 默认类目ID
            Integer brandId = context.getAttribute("brandId", 1); // 默认品牌ID  
            Integer modelId = context.getAttribute("modelId", 1); // 默认机型ID
            
            var template = qcTemplateManager.getRecyclingQcTemplate(businessLineId, cateId, brandId, modelId);
            context.setTemplate(template);
            
            log.info("回收质检模板查询成功, qcCode: {}, templateId: {}", 
                    command.getQcCode(), template != null ? template.getTemplateId() : null);
        } catch (Exception e) {
            log.error("查询回收质检模板失败, qcCode: {}, error: {}", command.getQcCode(), e.getMessage(), e);
            throw new RuntimeException("查询质检模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void generateReport(ReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("生成回收质检报告, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        var template = context.getTemplate();
        
        if (template == null) {
            throw new IllegalStateException("质检模板不能为空");
        }
        
        // 这里实现具体的报告生成逻辑
        // 1. 根据模板结构生成报告数据
        // 2. 处理质检项目和结果
        // 3. 计算评分等
        
        // 暂时创建一个简单的报告数据结构
        var reportData = new java.util.HashMap<String, Object>();
        reportData.put("qcCode", command.getQcCode());
        reportData.put("templateId", template.getTemplateId());
        reportData.put("businessLineId", command.getBusinessLineId());
        reportData.put("operatorId", command.getOperatorId());
        reportData.put("operatorName", command.getOperatorName());
        reportData.put("reportType", "recycling");
        reportData.put("generateTime", System.currentTimeMillis());
        
        context.setReportData(reportData);
        
        log.info("回收质检报告生成完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void mapReport(ReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("映射回收质检报告结果, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        SaveRecyclingReportDTO result = context.getResult();
        var reportData = context.getReportData();
        
        if (result == null) {
            result = createResult();
            context.setResult(result);
        }
        
        // 映射基本信息
        result.setQcCode(command.getQcCode());
        
        // 这里可以添加更多的映射逻辑
        // 例如：设置版本号、状态等
        if (reportData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            var dataMap = (java.util.Map<String, Object>) reportData;
            // 可以从reportData中提取更多信息设置到result中
        }
        
        log.info("回收质检报告结果映射完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void postProcess(ReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("回收质检报告后置处理, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        // 这里可以添加后置处理逻辑
        // 例如：发送通知、更新状态、记录日志等
        
        // 设置处理完成标识
        context.setAttribute("postProcessed", true);
        
        log.info("回收质检报告后置处理完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public SaveRecyclingReportDTO createResult() {
        return new SaveRecyclingReportDTO();
    }
}
