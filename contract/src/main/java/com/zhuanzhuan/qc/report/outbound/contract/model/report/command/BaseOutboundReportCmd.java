package com.zhuanzhuan.qc.report.outbound.contract.model.report.command;

import lombok.Data;

@Data
public class BaseOutboundReportCmd {

    /**
     * 质检码
     */
    private Long qcCode;

    /**
     * 业务线ID
     */
    private Long businessLineId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 报告类型标识
     */
    public String getReportType() {
        return this.getClass().getSimpleName().replace("Cmd", "");
    }
}
